// Application state model
export class AppModel {
  constructor() {
    this.state = {
      currentPage: 'welcome',
      isRecording: false,
      testText: "I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.",
      welcomeText: "Wanna test how good your speaking skill is?"
    };
    this.observers = [];
  }

  // Observer pattern for state changes
  addObserver(observer) {
    this.observers.push(observer);
  }

  removeObserver(observer) {
    this.observers = this.observers.filter(obs => obs !== observer);
  }

  notifyObservers(change) {
    this.observers.forEach(observer => observer.update(change));
  }

  // State getters
  getCurrentPage() {
    return this.state.currentPage;
  }

  getWelcomeText() {
    return this.state.welcomeText;
  }

  getTestText() {
    return this.state.testText;
  }

  isCurrentlyRecording() {
    return this.state.isRecording;
  }

  // State setters
  setCurrentPage(page) {
    const oldPage = this.state.currentPage;
    this.state.currentPage = page;
    this.notifyObservers({ 
      type: 'PAGE_CHANGE', 
      oldValue: oldPage, 
      newValue: page 
    });
  }

  setRecording(recording) {
    const oldRecording = this.state.isRecording;
    this.state.isRecording = recording;
    this.notifyObservers({ 
      type: 'RECORDING_CHANGE', 
      oldValue: oldRecording, 
      newValue: recording 
    });
  }

  // Actions
  startRecording() {
    this.setRecording(true);
  }

  stopRecording() {
    this.setRecording(false);
  }

  navigateToTest() {
    this.setCurrentPage('test');
  }

  navigateToWelcome() {
    this.setCurrentPage('welcome');
  }
}

// Create and export singleton instance
export const appModel = new AppModel();
