// Style components for the application
export const StyleComponents = {
  // Base styles
  body: {
    margin: '0',
    padding: '0',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    backgroundColor: '#f8f9fa',
    color: '#2c3e50',
    minHeight: '100vh',
    lineHeight: '1.6'
  },

  // Container styles
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '20px',
    boxSizing: 'border-box'
  },

  // Welcome page styles
  welcomeTitle: {
    fontSize: '2.5rem',
    fontWeight: '300',
    textAlign: 'center',
    marginBottom: '3rem',
    color: '#34495e',
    maxWidth: '800px',
    lineHeight: '1.4'
  },

  microphoneButton: {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    border: 'none',
    backgroundColor: '#3498db',
    color: 'white',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '2rem',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 15px rgba(52, 152, 219, 0.3)',
    outline: 'none'
  },

  microphoneButtonHover: {
    backgroundColor: '#2980b9',
    transform: 'scale(1.05)',
    boxShadow: '0 6px 20px rgba(52, 152, 219, 0.4)'
  },

  microphoneButtonActive: {
    backgroundColor: '#e74c3c',
    animation: 'pulse 1.5s infinite'
  },

  // Test page styles
  testContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minHeight: '100vh',
    padding: '40px 20px',
    boxSizing: 'border-box',
    position: 'relative'
  },

  testText: {
    fontSize: '1.8rem',
    fontWeight: '400',
    textAlign: 'center',
    marginTop: '10vh',
    marginBottom: '2rem',
    color: '#2c3e50',
    maxWidth: '1200px',
    lineHeight: '1.6',
    padding: '0 20px'
  },

  floatingMicrophone: {
    position: 'fixed',
    bottom: '300px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '70px',
    height: '70px',
    borderRadius: '50%',
    border: 'none',
    backgroundColor: '#e74c3c',
    color: 'white',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '1.8rem',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)',
    outline: 'none',
    zIndex: 1000
  },

  floatingMicrophoneHover: {
    backgroundColor: '#c0392b',
    transform: 'translateX(-50%) scale(1.05)',
    boxShadow: '0 6px 20px rgba(231, 76, 60, 0.4)'
  },

  // Icon styles
  microphoneIcon: {
    width: '24px',
    height: '24px',
    fill: 'currentColor'
  },

  // Utility classes
  hidden: {
    display: 'none'
  },

  fadeIn: {
    animation: 'fadeIn 0.5s ease-in'
  }
};

// CSS animations as strings to be injected
export const animations = `
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

// Helper function to apply styles to an element
export function applyStyles(element, styles) {
  Object.assign(element.style, styles);
}

// Helper function to inject CSS animations
export function injectAnimations() {
  const style = document.createElement('style');
  style.textContent = animations;
  document.head.appendChild(style);
}
