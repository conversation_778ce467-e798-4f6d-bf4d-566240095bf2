import { StyleComponents, applyStyles } from '../styles/components.js';
import microphoneIcon from '../assets/icons/microphone.svg?raw';

export class WelcomeView {
  constructor() {
    this.element = null;
    this.microphoneButton = null;
    this.onMicrophoneClick = null;
  }

  render() {
    // Create main container
    this.element = document.createElement('div');
    this.element.className = 'welcome-container';
    applyStyles(this.element, StyleComponents.container);

    // Create welcome title
    const title = document.createElement('h1');
    title.textContent = 'Wanna test how good your speaking skill is?';
    applyStyles(title, StyleComponents.welcomeTitle);

    // Create microphone button
    this.microphoneButton = document.createElement('button');
    this.microphoneButton.className = 'microphone-button';
    this.microphoneButton.innerHTML = microphoneIcon;
    applyStyles(this.microphoneButton, StyleComponents.microphoneButton);

    // Apply icon styles to the SVG
    const svgIcon = this.microphoneButton.querySelector('svg');
    if (svgIcon) {
      applyStyles(svgIcon, StyleComponents.microphoneIcon);
    }

    // Add hover effects
    this.microphoneButton.addEventListener('mouseenter', () => {
      applyStyles(this.microphoneButton, {
        ...StyleComponents.microphoneButton,
        ...StyleComponents.microphoneButtonHover
      });
    });

    this.microphoneButton.addEventListener('mouseleave', () => {
      applyStyles(this.microphoneButton, StyleComponents.microphoneButton);
    });

    // Add click handler
    this.microphoneButton.addEventListener('click', () => {
      if (this.onMicrophoneClick) {
        this.onMicrophoneClick();
      }
    });

    // Append elements
    this.element.appendChild(title);
    this.element.appendChild(this.microphoneButton);

    return this.element;
  }

  setMicrophoneClickHandler(handler) {
    this.onMicrophoneClick = handler;
  }

  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    this.element = null;
    this.microphoneButton = null;
  }

  getElement() {
    return this.element;
  }
}
