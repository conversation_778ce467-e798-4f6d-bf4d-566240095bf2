import { WelcomePresenter } from './WelcomePresenter.js';
import { TestPresenter } from './TestPresenter.js';
import { router } from '../utils/router.js';
import { appModel } from '../models/AppModel.js';
import { StyleComponents, applyStyles, injectAnimations } from '../styles/components.js';

export class AppPresenter {
  constructor() {
    this.currentPresenter = null;
    this.appContainer = null;
    this.model = appModel;
  }

  init() {
    // Set up the app container
    this.setupAppContainer();
    
    // Inject CSS animations
    injectAnimations();
    
    // Set up routes
    this.setupRoutes();
    
    // Subscribe to model changes
    this.model.addObserver(this);
    
    // Start the router
    router.handleRouteChange();
  }

  setupAppContainer() {
    // Apply base styles to body
    applyStyles(document.body, StyleComponents.body);
    
    // Create app container
    this.appContainer = document.getElementById('app');
    if (!this.appContainer) {
      this.appContainer = document.createElement('div');
      this.appContainer.id = 'app';
      document.body.appendChild(this.appContainer);
    }
  }

  setupRoutes() {
    // Register routes
    router.addRoute('/', () => this.showWelcome());
    router.addRoute('/welcome', () => this.showWelcome());
    router.addRoute('/test', () => this.showTest());
  }

  showWelcome() {
    this.destroyCurrentPresenter();
    
    this.currentPresenter = new WelcomePresenter();
    const viewElement = this.currentPresenter.init();
    
    this.appContainer.innerHTML = '';
    this.appContainer.appendChild(viewElement);
    
    // Add fade-in animation
    applyStyles(viewElement, StyleComponents.fadeIn);
    
    this.model.setCurrentPage('welcome');
  }

  showTest() {
    this.destroyCurrentPresenter();
    
    this.currentPresenter = new TestPresenter();
    const viewElement = this.currentPresenter.init();
    
    this.appContainer.innerHTML = '';
    this.appContainer.appendChild(viewElement);
    
    // Add fade-in animation
    applyStyles(viewElement, StyleComponents.fadeIn);
    
    this.model.setCurrentPage('test');
  }

  destroyCurrentPresenter() {
    if (this.currentPresenter) {
      this.currentPresenter.destroy();
      this.currentPresenter = null;
    }
  }

  // Observer pattern update method
  update(change) {
    // Handle model changes if needed
    console.log('App state changed:', change);
  }

  destroy() {
    this.destroyCurrentPresenter();
    this.model.removeObserver(this);
  }
}
