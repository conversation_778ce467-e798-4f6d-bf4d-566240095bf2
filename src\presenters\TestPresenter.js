import { TestView } from '../views/TestView.js';
import { appModel } from '../models/AppModel.js';

export class TestPresenter {
  constructor() {
    this.view = new TestView();
    this.model = appModel;
    this.isRecording = false;
  }

  init() {
    // Set up view event handlers
    this.view.setMicrophoneClickHandler(() => this.handleMicrophoneClick());
    
    // Subscribe to model changes
    this.model.addObserver(this);
    
    // Render the view with test text
    const testText = this.model.getTestText();
    const viewElement = this.view.render(testText);
    
    return viewElement;
  }

  handleMicrophoneClick() {
    if (this.isRecording) {
      this.stopRecording();
    } else {
      this.startRecording();
    }
  }

  startRecording() {
    this.isRecording = true;
    this.model.startRecording();
    this.view.updateRecordingState(true);
    
    // Here you would typically start actual audio recording
    console.log('Recording started...');
    
    // For demo purposes, auto-stop after 5 seconds
    setTimeout(() => {
      if (this.isRecording) {
        this.stopRecording();
      }
    }, 5000);
  }

  stopRecording() {
    this.isRecording = false;
    this.model.stopRecording();
    this.view.updateRecordingState(false);
    
    // Here you would typically stop audio recording and process the audio
    console.log('Recording stopped...');
  }

  // Observer pattern update method
  update(change) {
    if (change.type === 'RECORDING_CHANGE') {
      this.view.updateRecordingState(change.newValue);
    }
  }

  destroy() {
    // Unsubscribe from model
    this.model.removeObserver(this);
    
    if (this.view) {
      this.view.destroy();
    }
  }

  getView() {
    return this.view;
  }
}
