import { WelcomeView } from '../views/WelcomeView.js';
import { appModel } from '../models/AppModel.js';
import { router } from '../utils/router.js';

export class WelcomePresenter {
  constructor() {
    this.view = new WelcomeView();
    this.model = appModel;
  }

  init() {
    // Set up view event handlers
    this.view.setMicrophoneClickHandler(() => this.handleMicrophoneClick());
    
    // Render the view
    const viewElement = this.view.render();
    
    return viewElement;
  }

  handleMicrophoneClick() {
    // Navigate to test page
    router.navigate('/test');
  }

  destroy() {
    if (this.view) {
      this.view.destroy();
    }
  }

  getView() {
    return this.view;
  }
}
