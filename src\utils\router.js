// Hash-based router for SPA navigation
export class Router {
  constructor() {
    this.routes = new Map();
    this.currentRoute = null;
    this.init();
  }

  init() {
    // Listen for hash changes
    window.addEventListener('hashchange', () => this.handleRouteChange());
    window.addEventListener('load', () => this.handleRouteChange());
  }

  // Register a route with its handler
  addRoute(path, handler) {
    this.routes.set(path, handler);
  }

  // Navigate to a specific route
  navigate(path) {
    window.location.hash = path;
  }

  // Get current route from hash
  getCurrentRoute() {
    const hash = window.location.hash;
    return hash.slice(1) || '/'; // Remove # and default to '/'
  }

  // Handle route changes
  handleRouteChange() {
    const route = this.getCurrentRoute();
    const handler = this.routes.get(route);
    
    if (handler) {
      this.currentRoute = route;
      handler();
    } else {
      // Default route if not found
      const defaultHandler = this.routes.get('/');
      if (defaultHandler) {
        this.currentRoute = '/';
        defaultHandler();
      }
    }
  }

  // Get current route
  getRoute() {
    return this.currentRoute;
  }
}

// Create and export a singleton instance
export const router = new Router();
