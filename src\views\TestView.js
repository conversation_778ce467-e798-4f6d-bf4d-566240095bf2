import { StyleComponents, applyStyles } from '../styles/components.js';
import microphoneIcon from '../assets/icons/microphone.svg?raw';

export class TestView {
  constructor() {
    this.element = null;
    this.floatingMicrophone = null;
    this.onMicrophoneClick = null;
    this.isRecording = false;
  }

  render(testText) {
    // Create main container
    this.element = document.createElement('div');
    this.element.className = 'test-container';
    applyStyles(this.element, StyleComponents.testContainer);

    // Create test text
    const textElement = document.createElement('p');
    textElement.textContent = testText;
    applyStyles(textElement, StyleComponents.testText);

    // Create floating microphone button
    this.floatingMicrophone = document.createElement('button');
    this.floatingMicrophone.className = 'floating-microphone';
    this.floatingMicrophone.innerHTML = microphoneIcon;
    applyStyles(this.floatingMicrophone, StyleComponents.floatingMicrophone);

    // Apply icon styles to the SVG
    const svgIcon = this.floatingMicrophone.querySelector('svg');
    if (svgIcon) {
      applyStyles(svgIcon, StyleComponents.microphoneIcon);
    }

    // Add hover effects
    this.floatingMicrophone.addEventListener('mouseenter', () => {
      if (!this.isRecording) {
        applyStyles(this.floatingMicrophone, {
          ...StyleComponents.floatingMicrophone,
          ...StyleComponents.floatingMicrophoneHover
        });
      }
    });

    this.floatingMicrophone.addEventListener('mouseleave', () => {
      if (!this.isRecording) {
        applyStyles(this.floatingMicrophone, StyleComponents.floatingMicrophone);
      }
    });

    // Add click handler
    this.floatingMicrophone.addEventListener('click', () => {
      if (this.onMicrophoneClick) {
        this.onMicrophoneClick();
      }
    });

    // Append elements
    this.element.appendChild(textElement);
    document.body.appendChild(this.floatingMicrophone);

    return this.element;
  }

  setMicrophoneClickHandler(handler) {
    this.onMicrophoneClick = handler;
  }

  updateRecordingState(isRecording) {
    this.isRecording = isRecording;
    if (this.floatingMicrophone) {
      if (isRecording) {
        applyStyles(this.floatingMicrophone, {
          ...StyleComponents.floatingMicrophone,
          ...StyleComponents.microphoneButtonActive
        });
      } else {
        applyStyles(this.floatingMicrophone, StyleComponents.floatingMicrophone);
      }
    }
  }

  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    if (this.floatingMicrophone && this.floatingMicrophone.parentNode) {
      this.floatingMicrophone.parentNode.removeChild(this.floatingMicrophone);
    }
    this.element = null;
    this.floatingMicrophone = null;
  }

  getElement() {
    return this.element;
  }
}
